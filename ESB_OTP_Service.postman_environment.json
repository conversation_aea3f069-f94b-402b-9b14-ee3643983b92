{"id": "esb-otp-service-env", "name": "ESB OTP Service Environment", "values": [{"key": "base_url", "value": "http://localhost:3000/api/v1/esb", "type": "default", "enabled": true}, {"key": "test_email", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "test_msisdn", "value": "08123456789", "type": "default", "enabled": true}, {"key": "test_otp_token", "value": "123456", "type": "default", "enabled": true}, {"key": "transaction_id", "value": "", "type": "default", "enabled": true}, {"key": "server_host", "value": "localhost", "type": "default", "enabled": true}, {"key": "server_port", "value": "3000", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-06-24T12:55:00.000Z", "_postman_exported_using": "Postman/10.0.0"}