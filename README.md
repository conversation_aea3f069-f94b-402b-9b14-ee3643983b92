# ESB OTP Service for Multi-Factor Authentication (MFA)

A standalone Go service that provides **pure email-based** One-Time Password (OTP) functionality for Multi-Factor Authentication. This service integrates with Enterprise Service Bus (ESB) to send and validate OTP codes exclusively via email addresses.

## 🎯 Service Overview

This is a **pure email-based OTP generation service** designed for Multi-Factor Authentication (MFA) scenarios. It requires only email addresses and is primarily used for email OTP verification to continue login processes in other services.

### Key Features

- ✅ **Pure Email-based OTP Generation** - Send OTP codes using only email addresses
- ✅ **OTP Validation** - Verify OTP tokens with transaction tracking
- ✅ **Standalone Service** - No database or mobile number dependencies
- ✅ **ESB Integration** - Connects to Telkomsel's Enterprise Service Bus
- ✅ **Jakarta Timezone** - Uses Asia/Jakarta timezone for transaction IDs
- ✅ **RESTful API** - Clean HTTP endpoints with JSON responses
- ✅ **Health Monitoring** - Built-in health check endpoint
- ✅ **Comprehensive Logging** - Request/response logging for debugging

## 🚀 Quick Start

### Prerequisites

- Go 1.19 or higher
- Access to ESB API endpoints
- Environment variables configured

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd sa_esb_go
```

2. Install dependencies:

```bash
go mod download
```

3. Configure environment variables (see [Environment Configuration](#environment-configuration))

4. Build and run:

```bash
go build -o tmp/main.exe .
./tmp/main.exe
```

The service will start on port 3000 by default.

## 📚 API Documentation

### Base URL

```
http://localhost:3000/api/v1/esb
```

### Endpoints

#### 1. Health Check

**GET** `/health`

Check service health and status.

**Response:**

```json
{
  "meta": {
    "message": "ESB service is healthy",
    "code": 200,
    "status": "OK"
  },
  "data": {
    "service": "ESB OTP Service for Multi-Factor Authentication (MFA)",
    "description": "Pure email-based OTP generation service for MFA verification",
    "status": "healthy",
    "version": "1.0.0"
  }
}
```

#### 2. Send OTP Email

**POST** `/send-otp-email`

Send an OTP code to the specified email address.

**Request Body:**

```json
{
  "email": "<EMAIL>"
}
```

**Request Fields:**

- `email` (string, required): Email address to receive the OTP

**Response (Success):**

```json
{
  "meta": {
    "message": "OTP email sent successfully",
    "code": 200,
    "status": "OK"
  },
  "data": {
    "transaction_id": "SA-25062413184265788000",
    "status": "success",
    "message": "OTP email sent successfully",
    "data": {
      // ESB response data
    }
  }
}
```

**Response (Error):**

```json
{
  "meta": {
    "message": "Invalid MSISDN format",
    "code": 400,
    "status": "Error"
  },
  "data": null
}
```

#### 3. Submit OTP Email

**POST** `/submit-otp-email`

Validate an OTP token.

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "token": "123456",
  "transaction_id": "SA-25062413184265788000"
}
```

**Request Fields:**

- `email` (string, required): Email address that received the OTP
- `token` (string, required): OTP code to validate
- `transaction_id` (string, required): Transaction ID from send OTP response

**Response (Success):**

```json
{
  "meta": {
    "message": "OTP validation completed",
    "code": 200,
    "status": "OK"
  },
  "data": {
    "status": "success",
    "message": "OTP validation completed",
    "data": {
      // ESB validation response data
    }
  }
}
```

#### 4. Get OTP Status

**GET** `/otp-status/{transaction_id}`

Check the status of an OTP transaction.

**Response:**

```json
{
  "meta": {
    "message": "OTP status retrieved",
    "code": 200,
    "status": "OK"
  },
  "data": {
    "transaction_id": "SA-25062413184265788000",
    "status": "pending",
    "message": "OTP status check completed"
  }
}
```

## ⚙️ Environment Configuration

Create a `.env` file in the project root with the following variables:

```env
# Server Configuration
PORT=3000

# ESB API Configuration
ESB_URL=https://tdw.digitalcore.telkomsel.com
ESB_CP_NAME=api_byu
ESB_ELEMENT1=2020#13yU
ESB_CLIENT_ID=29
ESB_EMAIL_CLIENT_ID=35
ESB_API_KEY=your_api_key_here
ESB_SECRET=your_secret_here
ESB_CHANNEL=ia
ESB_HOST=tdw.digitalcore.telkomsel.com

# Optional: Proxy Configuration
PROXY_HOST=
PROXY_PORT=
PROXY_PROTOCOL=
```

### Required Environment Variables

| Variable              | Description              | Example                                 |
| --------------------- | ------------------------ | --------------------------------------- |
| `PORT`                | Server port              | `3000`                                  |
| `ESB_URL`             | ESB API base URL         | `https://tdw.digitalcore.telkomsel.com` |
| `ESB_CP_NAME`         | Content Provider name    | `api_byu`                               |
| `ESB_ELEMENT1`        | ESB element identifier   | `2020#13yU`                             |
| `ESB_CLIENT_ID`       | General client ID        | `29`                                    |
| `ESB_EMAIL_CLIENT_ID` | Email-specific client ID | `35`                                    |
| `ESB_API_KEY`         | API authentication key   | `your_api_key`                          |
| `ESB_SECRET`          | API secret for signature | `your_secret`                           |
| `ESB_CHANNEL`         | ESB channel identifier   | `ia`                                    |
| `ESB_HOST`            | ESB host header          | `tdw.digitalcore.telkomsel.com`         |

## 🏗️ Architecture

The service follows a clean architecture pattern with the following structure:

```
modules/esb/
├── http/
│   ├── handler.go      # HTTP request handlers
│   └── routes.go       # Route definitions
├── service/
│   ├── service.go      # Service interface
│   └── service_impl.go # Service implementation
└── repository/
    ├── repository.go      # Repository interface (for future use)
    └── repository_impl.go # Repository implementation
```

### Key Components

- **HTTP Layer**: Handles HTTP requests, validation, and responses
- **Service Layer**: Contains business logic for OTP operations
- **Repository Layer**: Reserved for future database operations (currently unused)

## 🔧 Development

### Building

```bash
go build -o tmp/main.exe .
```

### Running

```bash
./tmp/main.exe
```

### Testing Endpoints

Use the provided Postman collection or test manually with curl:

```bash
# Health check
curl -X GET http://localhost:3000/api/v1/esb/health

# Send OTP
curl -X POST http://localhost:3000/api/v1/esb/send-otp-email \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'

# Validate OTP
curl -X POST http://localhost:3000/api/v1/esb/submit-otp-email \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "token": "123456", "transaction_id": "SA-25062413184265788000"}'
```

## 📝 Logging

The service provides comprehensive logging for debugging and monitoring:

- **Request Logging**: All incoming requests are logged
- **ESB Communication**: Request/response data to/from ESB
- **Error Logging**: Detailed error information
- **Transaction Tracking**: Each OTP request gets a unique transaction ID

Log format example:

```
2025/06/24 13:18:42 MFA OTP Email Request: {Transaction:{TransactionID:SA-25062413184265788000 Channel:ia} Service:{ServiceID:<EMAIL>} OTP:{ClientID:35 Element1:2020#13yU CPName:api_byu EmailInfo:{Email:<EMAIL> Salutation:Bapak/Ibu FirstName:Pengguna LastName:Terhormat}}}
```

## 🔒 Security

- **API Key Authentication**: All ESB requests use API key authentication
- **Signature Verification**: MD5 signature for request integrity
- **Input Validation**: Email format validation
- **Error Handling**: Secure error responses without sensitive data exposure

## 📋 Error Codes

| HTTP Code | Description                 |
| --------- | --------------------------- |
| 200       | Success                     |
| 400       | Bad Request (invalid input) |
| 500       | Internal Server Error       |

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
