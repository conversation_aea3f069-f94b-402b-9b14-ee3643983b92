package http

import (
	"github.com/gofiber/fiber/v2"
)

// ESBRoutes defines all ESB module routes
func ESBRoutes(app fiber.Router, handler *ESBHandler) {
	// ESB OTP email routes
	app.Post("/send-otp-email", handler.SendOTPEmail)
	app.Post("/submit-otp-email", handler.SubmitOTPEmail)

	// Additional utility routes
	app.Get("/otp-status/:transaction_id", handler.GetOTPStatus)
	app.Get("/health", handler.HealthCheck)
}
