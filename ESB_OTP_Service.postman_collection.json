{"info": {"_postman_id": "esb-otp-service-mfa", "name": "ESB OTP Service for Multi-Factor Authentication (MFA)", "description": "A comprehensive collection for testing the ESB OTP Service API endpoints. This service provides email-based One-Time Password functionality for Multi-Factor Authentication scenarios.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "esb-otp-service"}, "item": [{"name": "Health Check", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has correct structure\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('meta');", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData.meta).to.have.property('status', 'OK');", "    pm.expect(jsonData.data).to.have.property('service');", "    pm.expect(jsonData.data).to.have.property('status', 'healthy');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Check the health status of the ESB OTP service. This endpoint returns service information and current status."}, "response": []}, {"name": "Send OTP Email", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200 or 500 (expected for demo)\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 500]);", "});", "", "pm.test(\"Response has correct structure\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('meta');", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData.meta).to.have.property('message');", "    pm.expect(jsonData.meta).to.have.property('code');", "    pm.expect(jsonData.meta).to.have.property('status');", "});", "", "// If successful, save transaction_id for next request", "if (pm.response.code === 200) {", "    const jsonData = pm.response.json();", "    if (jsonData.data && jsonData.data.transaction_id) {", "        pm.environment.set(\"transaction_id\", jsonData.data.transaction_id);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{test_email}}\",\n  \"msisdn\": \"{{test_msisdn}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/send-otp-email", "host": ["{{base_url}}"], "path": ["send-otp-email"]}, "description": "Send an OTP code to the specified email address. This endpoint initiates the MFA process by sending a one-time password to the user's email."}, "response": []}, {"name": "Submit OTP Email", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200 or 500 (expected for demo)\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 500]);", "});", "", "pm.test(\"Response has correct structure\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('meta');", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData.meta).to.have.property('message');", "    pm.expect(jsonData.meta).to.have.property('code');", "    pm.expect(jsonData.meta).to.have.property('status');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{test_email}}\",\n  \"msisdn\": \"{{test_msisdn}}\",\n  \"token\": \"{{test_otp_token}}\",\n  \"transaction_id\": \"{{transaction_id}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/submit-otp-email", "host": ["{{base_url}}"], "path": ["submit-otp-email"]}, "description": "Validate an OTP token. This endpoint completes the MFA process by verifying the OTP code entered by the user."}, "response": []}, {"name": "Get OTP Status", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has correct structure\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('meta');", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData.data).to.have.property('transaction_id');", "    pm.expect(jsonData.data).to.have.property('status');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/otp-status/{{transaction_id}}", "host": ["{{base_url}}"], "path": ["otp-status", "{{transaction_id}}"]}, "description": "Check the status of an OTP transaction using the transaction ID returned from the send OTP request."}, "response": []}, {"name": "Send OTP Email - Invalid MSISDN", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test(\"Error message for invalid MSISDN\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.meta.message).to.include('Invalid MSISDN format');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"msisdn\": \"invalid-msisdn\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/send-otp-email", "host": ["{{base_url}}"], "path": ["send-otp-email"]}, "description": "Test validation by sending an invalid MSISDN format. This should return a 400 error."}, "response": []}, {"name": "Submit OTP Email - Missing <PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test(\"Error message for missing fields\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.meta.status).to.equal('Error');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/submit-otp-email", "host": ["{{base_url}}"], "path": ["submit-otp-email"]}, "description": "Test validation by submitting incomplete request data. This should return a 400 error."}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:3000/api/v1/esb", "type": "string"}, {"key": "test_email", "value": "<EMAIL>", "type": "string"}, {"key": "test_msisdn", "value": "08123456789", "type": "string"}, {"key": "test_otp_token", "value": "123456", "type": "string"}, {"key": "transaction_id", "value": "SA-12345", "type": "string"}]}