# If you prefer the allow list template instead of the deny list, see community template:
# https://github.com/github/gitignore/blob/main/community/Golang/Go.AllowList.gitignore
#
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test
*.env

# Output of the go coverage tool, specifically when used with LiteIDE
*.out
*.log

# Dependency directories (remove the comment below to include it)
tmp/

# Go workspace file
go.work

# Air
*.air.toml