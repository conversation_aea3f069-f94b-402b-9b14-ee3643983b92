package service

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"
)

type esbService struct {
	url           string
	cpName        string
	element1      string
	clientID      string
	emailClientID string
	proxyHost     string
	proxyPort     string
	proxyProtocol string
	apiKey        string
	secret        string
	channel       string
	host          string
	httpClient    *http.Client
}

// NewESBService creates a new instance of ESB service
func NewESBService() ESBService {
	return &esbService{
		url:           os.Getenv("ESB_URL"),
		cpName:        os.Getenv("ESB_CP_NAME"),
		element1:      os.Getenv("ESB_ELEMENT1"),
		clientID:      os.Getenv("ESB_CLIENT_ID"),
		emailClientID: os.Getenv("ESB_EMAIL_CLIENT_ID"),
		proxyHost:     os.Getenv("PROXY_HOST"),
		proxyPort:     os.<PERSON>en<PERSON>("PROXY_PORT"),
		proxyProtocol: os.<PERSON>en<PERSON>("PROXY_PROTOCOL"),
		apiKey:        os.<PERSON>en<PERSON>("ESB_API_KEY"),
		secret:        os.<PERSON>en<PERSON>("ESB_SECRET"),
		channel:       os.Getenv("ESB_CHANNEL"),
		host:          os.Getenv("ESB_HOST"),
		httpClient:    &http.Client{Timeout: 30 * time.Second},
	}
}

// SendOTPEmail sends OTP email request to ESB
func (s *esbService) SendOTPEmail(ctx context.Context, email, msisdn string) (*OTPResponse, error) {
	// Generate timestamp and signature
	timestamp := time.Now().Unix()
	sig := s.generateSignature(timestamp)

	// Generate transaction ID
	transactionID := s.generateTransactionID(msisdn)

	// Build request payload
	requestPayload := OTPEmailRequest{
		Transaction: struct {
			TransactionID string `json:"transaction_id"`
			Channel       string `json:"channel"`
		}{
			TransactionID: transactionID,
			Channel:       s.channel,
		},
		Service: struct {
			ServiceID string `json:"service_id"`
		}{
			ServiceID: msisdn,
		},
		OTP: struct {
			ClientID  string `json:"client_id"`
			Element1  string `json:"element1"`
			CPName    string `json:"cp_name"`
			EmailInfo struct {
				Email      string `json:"email"`
				Salutation string `json:"salutation"`
				FirstName  string `json:"first_name"`
				LastName   string `json:"last_name"`
			} `json:"email_info"`
		}{
			ClientID: s.emailClientID,
			Element1: s.element1,
			CPName:   s.cpName,
			EmailInfo: struct {
				Email      string `json:"email"`
				Salutation string `json:"salutation"`
				FirstName  string `json:"first_name"`
				LastName   string `json:"last_name"`
			}{
				Email:      email,
				Salutation: "Kak",
				FirstName:  "",
				LastName:   "",
			},
		},
	}

	// Log request
	log.Printf("MFA OTP Email Request: %+v", requestPayload)

	// Make HTTP request
	response, err := s.makeHTTPRequest(ctx, "/scrt/esb/v2/otp/email/request", requestPayload, sig)
	if err != nil {
		return nil, fmt.Errorf("failed to send OTP email request: %w", err)
	}

	// Parse response
	otpResponse := &OTPResponse{
		TransactionID: transactionID,
		Status:        "success",
		Message:       "OTP email sent successfully",
		Data:          response,
	}

	log.Printf("MFA OTP Email Response: %+v", otpResponse)

	return otpResponse, nil
}

// SubmitOTPEmail validates OTP token
func (s *esbService) SubmitOTPEmail(ctx context.Context, email, msisdn, token, transactionID string) (*OTPValidationResponse, error) {
	// Generate timestamp and signature
	timestamp := time.Now().Unix()
	sig := s.generateSignature(timestamp)

	// Build request payload
	requestPayload := OTPValidationRequest{
		Transaction: struct {
			TransactionID string `json:"transaction_id"`
			Channel       string `json:"channel"`
		}{
			TransactionID: transactionID,
			Channel:       s.channel,
		},
		Service: struct {
			ServiceID string `json:"service_id"`
		}{
			ServiceID: msisdn,
		},
		OTP: struct {
			ClientID string `json:"client_id"`
			Element1 string `json:"element1"`
			CPName   string `json:"cp_name"`
			Token    string `json:"token"`
			Email    string `json:"email"`
		}{
			ClientID: s.emailClientID,
			Element1: s.element1,
			CPName:   s.cpName,
			Token:    token,
			Email:    email,
		},
	}

	// Log request
	log.Printf("MFA OTP Validation Request: %+v", requestPayload)

	// Make HTTP request
	response, err := s.makeHTTPRequest(ctx, "/scrt/esb/v2/otp/validation", requestPayload, sig)
	if err != nil {
		return nil, fmt.Errorf("failed to validate OTP: %w", err)
	}

	// Parse response
	validationResponse := &OTPValidationResponse{
		Status:  "success",
		Message: "OTP validation completed",
		Data:    response,
	}

	log.Printf("MFA OTP Validation Response: %+v", validationResponse)

	return validationResponse, nil
}

// generateSignature creates MD5 signature for authentication
func (s *esbService) generateSignature(timestamp int64) string {
	data := s.apiKey + s.secret + strconv.FormatInt(timestamp, 10)
	hash := md5.Sum([]byte(data))
	return fmt.Sprintf("%x", hash)
}

// generateTransactionID creates a unique transaction ID
func (s *esbService) generateTransactionID(msisdn string) string {
	now := time.Now()
	// Set timezone to Asia/Bangkok
	loc, _ := time.LoadLocation("Asia/Bangkok")
	bangkokTime := now.In(loc)

	// Get milliseconds
	milliseconds := bangkokTime.Nanosecond() / 1000000

	// Format date as ymdHis
	dateStr := bangkokTime.Format("060102150405")

	// Get last 5 digits of msisdn
	last5Msisdn := ""
	if len(msisdn) >= 5 {
		last5Msisdn = msisdn[len(msisdn)-5:]
	} else {
		last5Msisdn = msisdn
	}

	return fmt.Sprintf("SA-%s%d%s0", dateStr, milliseconds, last5Msisdn)
}

// makeHTTPRequest makes HTTP POST request to ESB API
func (s *esbService) makeHTTPRequest(ctx context.Context, endpoint string, payload interface{}, signature string) (map[string]interface{}, error) {
	// Convert payload to JSON
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request payload: %w", err)
	}

	// Create HTTP request
	url := s.url + endpoint
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("api_key", s.apiKey)
	req.Header.Set("x-signature", signature)
	req.Header.Set("Host", s.host)

	// Make request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make HTTP request: %w", err)
	}
	defer resp.Body.Close()

	// Parse response
	var response map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	// Check HTTP status
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP request failed with status %d: %v", resp.StatusCode, response)
	}

	return response, nil
}
