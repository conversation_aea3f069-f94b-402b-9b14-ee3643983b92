package repository

import (
	"context"
)

// ESBRepository defines the interface for ESB data operations
// Note: This interface is maintained for consistency with project structure
// even though the ESB service doesn't require database connectivity
type ESBRepository interface {
	// LogOTPRequest logs OTP request for audit purposes (if needed in future)
	LogOTPRequest(ctx context.Context, transactionID, msisdn string, requestData interface{}) error
	
	// LogOTPResponse logs OTP response for audit purposes (if needed in future)
	LogOTPResponse(ctx context.Context, transactionID string, responseData interface{}) error
}

// OTPLog represents an OTP transaction log entry
type OTPLog struct {
	ID            int         `json:"id"`
	TransactionID string      `json:"transaction_id"`
	MSISDN        string      `json:"msisdn"`
	RequestData   interface{} `json:"request_data"`
	ResponseData  interface{} `json:"response_data"`
	CreatedAt     string      `json:"created_at"`
	UpdatedAt     string      `json:"updated_at"`
}
