package http

import (
	"net/http"
	"sa_esb_go/helper"
	"sa_esb_go/modules/esb/service"
	"strconv"
	"strings"

	"github.com/gofiber/fiber/v2"
)

type ESBHandler struct {
	service    service.ESBService
}

// NewESBHandler creates a new instance of ESB handler
func NewESBHandler(service service.ESBService) *ESBHandler {
	return &ESBHandler{
		service: service,
	}
}

// SendOTPEmail handles OTP email request
func (h *<PERSON>SB<PERSON>andler) SendOTPEmail(c *fiber.Ctx) error {
	type SendOTPRequest struct {
		UserID int    `json:"user_id" validate:"required"`
		MSISDN string `json:"msisdn" validate:"required"`
	}

	var req SendOTPRequest
	if err := c.BodyParser(&req); err != nil {
		response := helper.APIResponse("Invalid request format", http.StatusBadRequest, "Error", nil)
		return c.Status(http.StatusBadRequest).JSON(response)
	}

	// Validate MSISDN format
	if !isValidMsisdnFormat(req.MSISDN) {
		response := helper.APIResponse("Invalid MSISDN format", http.StatusBadRequest, "Error", nil)
		return c.Status(http.StatusBadRequest).JSON(response)
	}

	// Format MSISDN to 628xx if it's in 08xx format
	msisdn := formatMsisdn(req.MSISDN)

	// Validate required fields
	if req.UserID <= 0 {
		response := helper.APIResponse("User ID is required", http.StatusBadRequest, "Error", nil)
		return c.Status(http.StatusBadRequest).JSON(response)
	}

	if msisdn == "" {
		response := helper.APIResponse("MSISDN is required", http.StatusBadRequest, "Error", nil)
		return c.Status(http.StatusBadRequest).JSON(response)
	}

	// Call service to send OTP email
	otpResponse, err := h.service.SendOTPEmail(c.Context(), req.UserID, msisdn)
	if err != nil {
		response := helper.APIResponse("Failed to send OTP email", http.StatusInternalServerError, "Error", err.Error())
		return c.Status(http.StatusInternalServerError).JSON(response)
	}

	response := helper.APIResponse("OTP email sent successfully", http.StatusOK, "OK", otpResponse)
	return c.Status(http.StatusOK).JSON(response)
}

// SubmitOTPEmail handles OTP validation request
func (h *ESBHandler) SubmitOTPEmail(c *fiber.Ctx) error {
	type SubmitOTPRequest struct {
		UserID        int    `json:"user_id" validate:"required"`
		MSISDN        string `json:"msisdn" validate:"required"`
		Token         string `json:"token" validate:"required"`
		TransactionID string `json:"transaction_id" validate:"required"`
	}

	var req SubmitOTPRequest
	if err := c.BodyParser(&req); err != nil {
		response := helper.APIResponse("Invalid request format", http.StatusBadRequest, "Error", nil)
		return c.Status(http.StatusBadRequest).JSON(response)
	}

	// Validate MSISDN format
	if !isValidMsisdnFormat(req.MSISDN) {
		response := helper.APIResponse("Invalid MSISDN format", http.StatusBadRequest, "Error", nil)
		return c.Status(http.StatusBadRequest).JSON(response)
	}

	// Format MSISDN to 628xx if it's in 08xx format
	msisdn := formatMsisdn(req.MSISDN)

	// Validate required fields
	if req.UserID <= 0 {
		response := helper.APIResponse("User ID is required", http.StatusBadRequest, "Error", nil)
		return c.Status(http.StatusBadRequest).JSON(response)
	}

	if msisdn == "" {
		response := helper.APIResponse("MSISDN is required", http.StatusBadRequest, "Error", nil)
		return c.Status(http.StatusBadRequest).JSON(response)
	}

	if req.Token == "" {
		response := helper.APIResponse("Token is required", http.StatusBadRequest, "Error", nil)
		return c.Status(http.StatusBadRequest).JSON(response)
	}

	if req.TransactionID == "" {
		response := helper.APIResponse("Transaction ID is required", http.StatusBadRequest, "Error", nil)
		return c.Status(http.StatusBadRequest).JSON(response)
	}

	// Call service to validate OTP
	validationResponse, err := h.service.SubmitOTPEmail(c.Context(), req.UserID, msisdn, req.Token, req.TransactionID)
	if err != nil {
		response := helper.APIResponse("Failed to validate OTP", http.StatusInternalServerError, "Error", err.Error())
		return c.Status(http.StatusInternalServerError).JSON(response)
	}

	response := helper.APIResponse("OTP validation completed", http.StatusOK, "OK", validationResponse)
	return c.Status(http.StatusOK).JSON(response)
}

// GetOTPStatus handles OTP status check (additional endpoint for convenience)
func (h *ESBHandler) GetOTPStatus(c *fiber.Ctx) error {
	transactionID := c.Params("transaction_id")
	
	if transactionID == "" {
		response := helper.APIResponse("Transaction ID is required", http.StatusBadRequest, "Error", nil)
		return c.Status(http.StatusBadRequest).JSON(response)
	}

	// For now, return a simple status response
	// In a real implementation, you might check the status from ESB or database
	statusData := map[string]interface{}{
		"transaction_id": transactionID,
		"status":         "pending",
		"message":        "OTP status check completed",
	}

	response := helper.APIResponse("OTP status retrieved", http.StatusOK, "OK", statusData)
	return c.Status(http.StatusOK).JSON(response)
}

// Health check endpoint
func (h *ESBHandler) HealthCheck(c *fiber.Ctx) error {
	healthData := map[string]interface{}{
		"service": "ESB OTP Service",
		"status":  "healthy",
		"version": "1.0.0",
	}

	response := helper.APIResponse("ESB service is healthy", http.StatusOK, "OK", healthData)
	return c.Status(http.StatusOK).JSON(response)
}

// Helper functions (similar to user module)
func isValidMsisdnFormat(msisdn string) bool {
	return strings.HasPrefix(msisdn, "08") || strings.HasPrefix(msisdn, "628")
}

func formatMsisdn(msisdn string) string {
	if strings.HasPrefix(msisdn, "08") {
		return "62" + msisdn[1:]
	}
	return msisdn
}

// Helper function to convert string to int safely
func stringToInt(s string) int {
	if i, err := strconv.Atoi(s); err == nil {
		return i
	}
	return 0
}
