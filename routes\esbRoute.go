package routes

import (
	"sa_esb_go/modules/esb/http"
	"sa_esb_go/modules/esb/service"

	"github.com/gofiber/fiber/v2"
)

// ESBRouter sets up ESB routes and dependencies
func ESBRouter(app fiber.Router) {
	// Initialize repository (no database needed for current implementation)
	// esbRepo := repository.NewESBRepository() // Reserved for future use

	// Initialize service
	esbService := service.NewESBService()

	// Initialize handler
	esbHandler := http.NewESBHandler(esbService)

	// Register routes
	http.ESBRoutes(app, esbHandler)
}
