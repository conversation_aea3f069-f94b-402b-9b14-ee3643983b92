# ESB OTP Service API Reference

## Base URL

```
http://localhost:3000/api/v1/esb
```

## Authentication

The service uses ESB API key authentication configured via environment variables. No additional authentication is required for the API endpoints.

## Common Response Format

All API responses follow this structure:

```json
{
  "meta": {
    "message": "Response message",
    "code": 200,
    "status": "OK"
  },
  "data": {
    // Response data or null
  }
}
```

## Endpoints

### 1. Health Check

**Endpoint:** `GET /health`

**Description:** Check service health and status

**Response:**

```json
{
  "meta": {
    "message": "ESB service is healthy",
    "code": 200,
    "status": "OK"
  },
  "data": {
    "service": "ESB OTP Service for Multi-Factor Authentication (MFA)",
    "description": "General OTP generation service for email OTP verification",
    "status": "healthy",
    "version": "1.0.0"
  }
}
```

---

### 2. Send OTP Email

**Endpoint:** `POST /send-otp-email`

**Description:** Send OTP code to email address for MFA

**Request Body:**

```json
{
  "email": "<EMAIL>"
}
```

**Request Fields:**

- `email` (string, required): Valid email address

**Success Response (200):**

```json
{
  "meta": {
    "message": "OTP email sent successfully",
    "code": 200,
    "status": "OK"
  },
  "data": {
    "transaction_id": "SA-250624125438112567890",
    "status": "success",
    "message": "OTP email sent successfully",
    "data": {
      // ESB response data
    }
  }
}
```

**Error Response (400):**

```json
{
  "meta": {
    "message": "Invalid MSISDN format",
    "code": 400,
    "status": "Error"
  },
  "data": null
}
```

---

### 3. Submit OTP Email

**Endpoint:** `POST /submit-otp-email`

**Description:** Validate OTP token for MFA completion

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "msisdn": "08123456789",
  "token": "123456",
  "transaction_id": "SA-250624125438112567890"
}
```

**Request Fields:**

- `email` (string, required): Email address that received the OTP
- `msisdn` (string, required): Indonesian mobile number (08xxx or 628xxx format)
- `token` (string, required): OTP code to validate
- `transaction_id` (string, required): Transaction ID from send OTP response

**Success Response (200):**

```json
{
  "meta": {
    "message": "OTP validation completed",
    "code": 200,
    "status": "OK"
  },
  "data": {
    "status": "success",
    "message": "OTP validation completed",
    "data": {
      // ESB validation response data
    }
  }
}
```

**Error Response (400):**

```json
{
  "meta": {
    "message": "Email is required",
    "code": 400,
    "status": "Error"
  },
  "data": null
}
```

---

### 4. Get OTP Status

**Endpoint:** `GET /otp-status/{transaction_id}`

**Description:** Check OTP transaction status

**Path Parameters:**

- `transaction_id` (string, required): Transaction ID from send OTP response

**Success Response (200):**

```json
{
  "meta": {
    "message": "OTP status retrieved",
    "code": 200,
    "status": "OK"
  },
  "data": {
    "transaction_id": "SA-250624125438112567890",
    "status": "pending",
    "message": "OTP status check completed"
  }
}
```

---

## Error Codes

| HTTP Code | Description           | Common Causes                               |
| --------- | --------------------- | ------------------------------------------- |
| 200       | Success               | Request processed successfully              |
| 400       | Bad Request           | Invalid input data, missing required fields |
| 500       | Internal Server Error | ESB communication error, server issues      |

## MSISDN Format

The service accepts Indonesian mobile numbers in two formats:

- **08xxx format**: `08123456789` (automatically converted to 628xxx)
- **628xxx format**: `628123456789` (international format)

## Transaction ID Format

Transaction IDs are automatically generated with the format:

```
SA-{YYMMDDHHMMSS}{milliseconds}{last5digits}0
```

Example: `SA-250624125438112567890`

## Testing with cURL

### Send OTP Email

```bash
curl -X POST http://localhost:3000/api/v1/esb/send-otp-email \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "msisdn": "08123456789"
  }'
```

### Submit OTP Email

```bash
curl -X POST http://localhost:3000/api/v1/esb/submit-otp-email \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "msisdn": "08123456789",
    "token": "123456",
    "transaction_id": "SA-250624125438112567890"
  }'
```

### Health Check

```bash
curl -X GET http://localhost:3000/api/v1/esb/health
```

### Get OTP Status

```bash
curl -X GET http://localhost:3000/api/v1/esb/otp-status/SA-250624125438112567890
```

## Rate Limiting

Currently, no rate limiting is implemented. Consider implementing rate limiting for production use.

## Logging

All requests and responses are logged for debugging purposes. Check server logs for detailed information about ESB communication.

## Support

For issues or questions, refer to the main README.md file or check the server logs for detailed error information.
