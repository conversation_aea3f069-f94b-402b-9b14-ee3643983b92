package service

import (
	"context"
)

// ESBService defines the interface for ESB OTP operations
// This is a pure email-based OTP generation service for Multi-Factor Authentication (MFA)
// Used for email OTP verification to continue login in other services
type ESBService interface {
	SendOTPEmail(ctx context.Context, email string) (*OTPResponse, error)
	SubmitOTPEmail(ctx context.Context, email, token, transactionID string) (*OTPValidationResponse, error)
}

// OTPResponse represents the response from OTP email request
type OTPResponse struct {
	TransactionID string      `json:"transaction_id"`
	Status        string      `json:"status"`
	Message       string      `json:"message"`
	Data          interface{} `json:"data,omitempty"`
}

// OTPValidationResponse represents the response from OTP validation
type OTPValidationResponse struct {
	Status  string      `json:"status"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// OTPEmailRequest represents the request structure for OTP email
type OTPEmailRequest struct {
	Transaction struct {
		TransactionID string `json:"transaction_id"`
		Channel       string `json:"channel"`
	} `json:"transaction"`
	Service struct {
		ServiceID string `json:"service_id"`
	} `json:"service"`
	OTP struct {
		ClientID  string `json:"client_id"`
		Element1  string `json:"element1"`
		CPName    string `json:"cp_name"`
		EmailInfo struct {
			Email      string `json:"email"`
			Salutation string `json:"salutation"`
			FirstName  string `json:"first_name"`
			LastName   string `json:"last_name"`
		} `json:"email_info"`
	} `json:"otp"`
}

// OTPValidationRequest represents the request structure for OTP validation
type OTPValidationRequest struct {
	Transaction struct {
		TransactionID string `json:"transaction_id"`
		Channel       string `json:"channel"`
	} `json:"transaction"`
	Service struct {
		ServiceID string `json:"service_id"`
	} `json:"service"`
	OTP struct {
		ClientID string `json:"client_id"`
		Element1 string `json:"element1"`
		CPName   string `json:"cp_name"`
		Token    string `json:"token"`
		Email    string `json:"email"`
	} `json:"otp"`
}
