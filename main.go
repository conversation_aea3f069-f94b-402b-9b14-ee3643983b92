package main

import (
	"log"
	"sa_esb_go/config"

	"github.com/joho/godotenv"
)

func main() {
	err := godotenv.Load()
	if err != nil {
		log.Println(".env file not found, using environment variables instead")
	}

	// Try to connect to database, but don't fail if it's not available
	db, dbErr := config.TryConnect()
	if dbErr != nil {
		log.Printf("Database connection failed: %v. Running with limited functionality.", dbErr)
	}

	config.Route(db)
}
